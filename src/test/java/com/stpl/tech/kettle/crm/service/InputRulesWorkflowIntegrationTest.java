package com.stpl.tech.kettle.crm.service;

import com.stpl.tech.kettle.crm.service.impl.InputRulesWorkflowExample;
import com.stpl.tech.kettle.crm.util.InputRulesWorkflowUtils;
import com.stpl.tech.kettle.domain.kettle.model.CartRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.CartRulesDecisionResult;
import com.stpl.tech.kettle.domain.kettle.model.InputRulesDroolDecisionProperties;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for the complete Input Rules Workflow
 * Tests the entire sequence from Group Definition sheet to rule definitions
 */
@SpringBootTest
@Log4j2
public class InputRulesWorkflowIntegrationTest {

    @Autowired
    private InputRulesWorkflowService inputRulesWorkflowService;

    @Autowired
    private InputRulesProcessingService inputRulesProcessingService;

    @Autowired
    private InputRulesWorkflowExample workflowExample;

    @Test
    public void testCompleteWorkflow() {
        log.info("Testing complete input rules workflow");

        // Step 1: Simulate Group Definition sheet result
        String groupDefinitionResult = "1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16";

        // Step 2: Create CartRulesDroolDecisionProperties
        CartRulesDroolDecisionProperties cartRulesProperties = createTestCartRulesProperties();

        // Step 3: Execute complete workflow
        InputRulesWorkflowService.InputRulesWorkflowResult result = 
            inputRulesWorkflowService.executeCompleteWorkflow(groupDefinitionResult, cartRulesProperties, "v1.0");

        // Step 4: Verify results
        assertNotNull(result);
        assertEquals(groupDefinitionResult, result.getOriginalGroupDefinitionResult());
        assertNotNull(result.getParsedRuleNumbers());
        assertEquals(16, result.getParsedRuleNumbers().size());
        assertTrue(result.getParsedRuleNumbers().contains(1));
        assertTrue(result.getParsedRuleNumbers().contains(16));

        // Verify CartRulesDecisionProperties was updated
        assertNotNull(result.getUpdatedCartRulesProperties());
        assertEquals(groupDefinitionResult, result.getUpdatedCartRulesProperties().getInputRulesToBeRun());

        // Verify CartRulesDecisionResult was created
        assertNotNull(result.getCartRulesDecisionResult());
        assertEquals(16, result.getCartRulesDecisionResult().getInputRuleNumbers().size());

        log.info("Complete workflow test passed successfully");
    }

    @Test
    public void testStepByStepWorkflow() {
        log.info("Testing step-by-step workflow");

        String groupDefinitionResult = "1_3_5_7_9";

        // Step 1-2: Set input rules from Group Definition
        CartRulesDroolDecisionProperties cartRulesProperties = createTestCartRulesProperties();
        CartRulesDroolDecisionProperties updatedProperties = 
            inputRulesWorkflowService.setInputRulesFromGroupDefinition(groupDefinitionResult, cartRulesProperties);

        assertNotNull(updatedProperties);
        assertEquals(groupDefinitionResult, updatedProperties.getInputRulesToBeRun());

        // Step 3-4: Parse and update decision result
        CartRulesDecisionResult decisionResult = 
            inputRulesWorkflowService.parseAndUpdateDecisionResult(updatedProperties);

        assertNotNull(decisionResult);
        assertNotNull(decisionResult.getInputRuleNumbers());
        assertEquals(5, decisionResult.getInputRuleNumbers().size());
        assertTrue(decisionResult.getInputRuleNumbers().contains(1));
        assertTrue(decisionResult.getInputRuleNumbers().contains(9));

        // Step 5-6: Execute input rules and get definitions
        List<InputRulesDroolDecisionProperties> ruleDefinitions = 
            inputRulesWorkflowService.executeInputRulesAndGetDefinitions(decisionResult, "v1.0");

        assertNotNull(ruleDefinitions);
        // Note: Actual rule definitions depend on the Excel file content

        log.info("Step-by-step workflow test passed successfully");
    }

    @Test
    public void testUnderscoreSeparatedStringParsing() {
        log.info("Testing underscore-separated string parsing");

        // Test valid format
        String validString = "1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16";
        List<Integer> parsed = inputRulesWorkflowService.parseUnderscoreSeparatedString(validString);

        assertNotNull(parsed);
        assertEquals(16, parsed.size());
        assertEquals(Integer.valueOf(1), parsed.get(0));
        assertEquals(Integer.valueOf(16), parsed.get(15));

        // Test conversion back to string
        String converted = inputRulesWorkflowService.convertToUnderscoreSeparatedString(parsed);
        assertEquals(validString, converted);

        // Test edge cases
        assertTrue(inputRulesWorkflowService.parseUnderscoreSeparatedString("").isEmpty());
        assertTrue(inputRulesWorkflowService.parseUnderscoreSeparatedString(null).isEmpty());

        log.info("String parsing test passed successfully");
    }

    @Test
    public void testUtilityMethods() {
        log.info("Testing utility methods");

        // Test validation methods
        String validFormat = "1_2_3_4_5";
        String invalidFormat = "1,2,3,4,5";

        assertTrue(InputRulesWorkflowUtils.isValidUnderscoreSeparatedFormat(validFormat));
        assertFalse(InputRulesWorkflowUtils.isValidUnderscoreSeparatedFormat(invalidFormat));

        // Test CartRulesProperties validation
        CartRulesDroolDecisionProperties validProperties = createTestCartRulesProperties();
        validProperties.setInputRulesToBeRun(validFormat);
        assertTrue(InputRulesWorkflowUtils.isValidForInputRulesProcessing(validProperties));

        CartRulesDroolDecisionProperties invalidProperties = createTestCartRulesProperties();
        invalidProperties.setInputRulesToBeRun(null);
        assertFalse(InputRulesWorkflowUtils.isValidForInputRulesProcessing(invalidProperties));

        // Test success rate calculation
        double successRate = InputRulesWorkflowUtils.calculateSuccessRate(10, 8);
        assertEquals(80.0, successRate, 0.01);

        log.info("Utility methods test passed successfully");
    }

    @Test
    public void testWorkflowExample() {
        log.info("Testing workflow example");

        // Test the example methods
        assertDoesNotThrow(() -> {
            workflowExample.demonstrateCompleteWorkflow();
            workflowExample.demonstrateStepByStepWorkflow();
        });

        // Test the API-style method
        String groupDefinitionResult = "1_2_3_4_5";
        CartRulesDroolDecisionProperties cartRulesProperties = createTestCartRulesProperties();
        
        List<InputRulesDroolDecisionProperties> result = 
            workflowExample.processGroupDefinitionResult(groupDefinitionResult, cartRulesProperties, "v1.0");

        assertNotNull(result);
        // Note: Result size depends on actual Excel file content

        log.info("Workflow example test passed successfully");
    }

    @Test
    public void testErrorHandling() {
        log.info("Testing error handling");

        // Test with null inputs
        InputRulesWorkflowService.InputRulesWorkflowResult result1 = 
            inputRulesWorkflowService.executeCompleteWorkflow(null, null, "v1.0");
        assertNotNull(result1);
        assertFalse(result1.isSuccessful());

        // Test with invalid format
        InputRulesWorkflowService.InputRulesWorkflowResult result2 = 
            inputRulesWorkflowService.executeCompleteWorkflow("1,2,3,4,5", createTestCartRulesProperties(), "v1.0");
        assertNotNull(result2);

        // Test with empty string
        InputRulesWorkflowService.InputRulesWorkflowResult result3 = 
            inputRulesWorkflowService.executeCompleteWorkflow("", createTestCartRulesProperties(), "v1.0");
        assertNotNull(result3);

        log.info("Error handling test passed successfully");
    }

    /**
     * Creates test CartRulesDroolDecisionProperties
     */
    private CartRulesDroolDecisionProperties createTestCartRulesProperties() {
        return CartRulesDroolDecisionProperties.builder()
                .lastOrderCategory("MIXED")
                .minPreviousCartATV(new BigDecimal("150.00"))
                .maxPreviousCartATV(new BigDecimal("500.00"))
                .customerFrequency("ALF")
                .timeOfDay("EVENING_1")
                .customerNumberAvailable(true)
                .currentCartState("ACTIVE")
                .minCurrentCartATV(new BigDecimal("200.00"))
                .maxCurrentCartATV(new BigDecimal("600.00"))
                .build();
    }
}
