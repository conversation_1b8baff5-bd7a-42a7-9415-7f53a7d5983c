# Complete Input Rules Processing Workflow Guide

This guide explains the complete workflow for processing input rules from Group Definition sheet to rule definitions.

## Workflow Overview

The complete workflow handles the following sequence:

1. **Extract input rules from Group Definition sheet** → Returns "1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16"
2. **Set in CartRulesDroolDecisionProperties** → Store in `inputRulesToBeRun` field
3. **Parse and convert format** → Convert to List<Integer> [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]
4. **Update CartRulesDecisionResult** → Set parsed list in `inputRuleNumbers` field
5. **Execute input rules processing** → Feed each rule number into InputRulesDroolInitialiser
6. **Return rule definitions** → Get complete InputRulesDroolDecisionProperties for each rule

## Implementation

### Complete Workflow Service

```java
@Autowired
private InputRulesWorkflowService inputRulesWorkflowService;

public void processGroupDefinitionResult() {
    // Step 1: Group Definition sheet returns underscore-separated format
    String groupDefinitionResult = "1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16";
    
    // Step 2: Create CartRulesDroolDecisionProperties
    CartRulesDroolDecisionProperties cartRulesProperties = CartRulesDroolDecisionProperties.builder()
        .lastOrderCategory("MIXED")
        .customerFrequency("ALF")
        .timeOfDay("EVENING_1")
        .currentCartState("ACTIVE")
        .build();
    
    // Step 3: Execute complete workflow
    InputRulesWorkflowService.InputRulesWorkflowResult result = 
        inputRulesWorkflowService.executeCompleteWorkflow(groupDefinitionResult, cartRulesProperties, "v1.0");
    
    // Step 4: Get results
    List<InputRulesDroolDecisionProperties> ruleDefinitions = result.getInputRuleDefinitions();
    List<Integer> parsedRuleNumbers = result.getParsedRuleNumbers();
    
    // Each InputRulesDroolDecisionProperties contains complete rule definition:
    for (InputRulesDroolDecisionProperties rule : ruleDefinitions) {
        System.out.println("Rule Number: " + rule.getRuleNum());
        System.out.println("Previous Purchases: " + rule.getPreviousPurchases());
        System.out.println("Customer Frequency: " + rule.getCustomerFrequency());
        System.out.println("Time of Day: " + rule.getTimeOfDay());
        System.out.println("Output Rules to Run: " + rule.getOutputRulesToBeRun());
        // ... all other fields populated from Excel sheet
    }
}
```

### Step-by-Step Processing

```java
public void stepByStepProcessing() {
    String groupDefinitionResult = "1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16";
    
    // Step 1-2: Set input rules from Group Definition
    CartRulesDroolDecisionProperties cartRulesProperties = createCartRulesProperties();
    CartRulesDroolDecisionProperties updatedProperties = 
        inputRulesWorkflowService.setInputRulesFromGroupDefinition(groupDefinitionResult, cartRulesProperties);
    
    // Verify: updatedProperties.getInputRulesToBeRun() == "1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16"
    
    // Step 3-4: Parse underscore format and update CartRulesDecisionResult
    CartRulesDecisionResult decisionResult = 
        inputRulesWorkflowService.parseAndUpdateDecisionResult(updatedProperties);
    
    // Verify: decisionResult.getInputRuleNumbers() == [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]
    
    // Step 5-6: Execute input rules and get definitions
    List<InputRulesDroolDecisionProperties> ruleDefinitions = 
        inputRulesWorkflowService.executeInputRulesAndGetDefinitions(decisionResult, "v1.0");
    
    // Result: List of complete rule definitions, one for each rule number
}
```

## Data Flow

### Input Format (Group Definition Sheet)
```
"1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16"
```

### Intermediate Format (CartRulesDroolDecisionProperties)
```java
cartRulesProperties.setInputRulesToBeRun("1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16");
```

### Parsed Format (CartRulesDecisionResult)
```java
cartRulesDecisionResult.setInputRuleNumbers([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]);
```

### Output Format (List<InputRulesDroolDecisionProperties>)
```java
[
  InputRulesDroolDecisionProperties{ruleNum=1, previousPurchases=0, customerFrequency="NEW", ...},
  InputRulesDroolDecisionProperties{ruleNum=2, previousPurchases=5, customerFrequency="ALF", ...},
  InputRulesDroolDecisionProperties{ruleNum=3, previousPurchases=15, customerFrequency="AHF", ...},
  // ... definitions for rules 4-16
]
```

## Utility Methods

### Format Conversion
```java
// Parse underscore-separated string
List<Integer> ruleNumbers = InputRulesWorkflowUtils.parseUnderscoreSeparatedString("1_2_3_4_5");
// Result: [1, 2, 3, 4, 5]

// Convert back to underscore-separated string
String ruleString = InputRulesWorkflowUtils.convertToUnderscoreSeparatedString([1, 2, 3, 4, 5]);
// Result: "1_2_3_4_5"
```

### Validation
```java
// Validate format
boolean isValid = InputRulesWorkflowUtils.isValidUnderscoreSeparatedFormat("1_2_3_4_5");

// Validate CartRulesProperties
boolean canProcess = InputRulesWorkflowUtils.isValidForInputRulesProcessing(cartRulesProperties);
```

## Error Handling

The workflow includes comprehensive error handling:

```java
InputRulesWorkflowService.InputRulesWorkflowResult result = 
    inputRulesWorkflowService.executeCompleteWorkflow(groupDefinitionResult, cartRulesProperties, version);

if (result.isSuccessful()) {
    // Process successful results
    List<InputRulesDroolDecisionProperties> ruleDefinitions = result.getInputRuleDefinitions();
    int totalProcessed = result.getTotalRulesProcessed();
} else {
    // Handle errors
    log.warn("Input rules processing failed");
}
```

## Integration Examples

### In Cart Rules Recommendation API
```java
@PostMapping("/get-cart-rules-recommendations")
public ResponseEntity<CartRulesRecommendationResponse> getCartRulesRecommendations(
        @RequestBody CartRulesRecommendationRequest request) {
    
    // Get Group Definition result (from your existing logic)
    String groupDefinitionResult = getGroupDefinitionResult(request);
    
    // Process input rules
    CartRulesDroolDecisionProperties cartRulesProperties = buildCartRulesProperties(request);
    InputRulesWorkflowService.InputRulesWorkflowResult inputRulesResult = 
        inputRulesWorkflowService.executeCompleteWorkflow(groupDefinitionResult, cartRulesProperties, version);
    
    // Use input rule definitions for further processing
    List<InputRulesDroolDecisionProperties> inputRuleDefinitions = inputRulesResult.getInputRuleDefinitions();
    
    // Continue with your existing cart rules logic...
}
```

### In Standalone Input Rules API
```java
@PostMapping("/process-input-rules")
public ResponseEntity<List<InputRulesDroolDecisionProperties>> processInputRules(
        @RequestParam String groupDefinitionResult,
        @RequestBody CartRulesDroolDecisionProperties cartRulesProperties) {
    
    InputRulesWorkflowService.InputRulesWorkflowResult result = 
        inputRulesWorkflowService.executeCompleteWorkflow(groupDefinitionResult, cartRulesProperties, "v1.0");
    
    return ResponseEntity.ok(result.getInputRuleDefinitions());
}
```

## Testing

Comprehensive tests are available in `InputRulesWorkflowIntegrationTest.java`:

```java
@Test
public void testCompleteWorkflow() {
    String groupDefinitionResult = "1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16";
    CartRulesDroolDecisionProperties cartRulesProperties = createTestProperties();
    
    InputRulesWorkflowService.InputRulesWorkflowResult result = 
        inputRulesWorkflowService.executeCompleteWorkflow(groupDefinitionResult, cartRulesProperties, "v1.0");
    
    assertEquals(16, result.getParsedRuleNumbers().size());
    assertTrue(result.isSuccessful());
}
```

## Performance Considerations

- **Batch Processing**: The workflow processes all rule numbers in a single operation
- **Caching**: Drool containers are cached per version
- **Error Recovery**: Individual rule failures don't stop the entire workflow
- **Logging**: Comprehensive logging for monitoring and debugging

## Troubleshooting

1. **Invalid Format**: Ensure Group Definition result uses underscore separation
2. **Missing Rules**: Check that Excel file contains definitions for all rule numbers
3. **Version Issues**: Verify the correct version parameter is passed
4. **Performance**: Monitor processing time for large rule sets
