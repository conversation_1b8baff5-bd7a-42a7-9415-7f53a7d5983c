package com.stpl.tech.kettle.crm.service.impl;

import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.stpl.tech.common.cache.ProductCache;
import com.stpl.tech.kettle.crm.cache.UnitDroolVersionMappingCache;
import com.stpl.tech.kettle.crm.cache.impl.CrmCacheService;
import com.stpl.tech.kettle.crm.data.kettle.CustomerInfo;
import com.stpl.tech.kettle.crm.data.kettle.OrderDetail;
import com.stpl.tech.kettle.crm.exception.DataUpdationException;
import com.stpl.tech.kettle.crm.repository.clm.CustomerFavouriteProductsDao;
import com.stpl.tech.kettle.crm.repository.kettle.OrderDetailDao;
import com.stpl.tech.kettle.crm.service.CartRulesRecommendationService;
import com.stpl.tech.kettle.crm.service.CustomerService;
import com.stpl.tech.kettle.crm.service.InputRulesWorkflowService;
import com.stpl.tech.kettle.crm.util.AppConstants;
import com.stpl.tech.kettle.crm.util.AppUtils;
import com.stpl.tech.kettle.crm.util.DroolFileType;
import com.stpl.tech.kettle.domain.kettle.model.CartRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.CartRulesDecisionInputResult;
import com.stpl.tech.kettle.domain.kettle.model.InputRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.OutputRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.RecommendedProduct;
import com.stpl.tech.kettle.domain.kettle.model.recom.CustomerRecommendationDroolProperties;
import com.stpl.tech.kettle.domain.kettle.model.request.CartRulesRecommendationRequest;
import com.stpl.tech.kettle.domain.kettle.model.response.CartRulesRecommendationResponse;
import com.stpl.tech.kettle.domain.master.model.recom.RecommendationDayPart;
import com.stpl.tech.master.domain.model.DroolVersionDomain;
import com.stpl.tech.master.domain.model.ProductVO;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Cart Rules Recommendation Service Implementation
 * Implements cart-based recommendation processing using drool rules
 */
@Service
@Log4j2
public class CartRulesRecommendationServiceImpl implements CartRulesRecommendationService {

    @Autowired
    private CartRulesOrchestrationService cartRulesOrchestrationService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private CustomerFavouriteProductsDao customerFavouriteProductsDao;

    @Autowired
    private ProductCache productCache;

    @Autowired
    private CrmCacheService crmCacheService;

    @Autowired
    private OrderDetailDao orderDetailDao;

    @Autowired
    private UnitDroolVersionMappingCache unitDroolVersionMappingCache;

    @Override
    public CartRulesRecommendationResponse getCartBasedRecommendations(CartRulesRecommendationRequest request) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        Map<String, DroolVersionDomain> unitDroolVersionMapping =  unitDroolVersionMappingCache.getUnitDroolVersionMapping(request.getUnitId());
        log.info("Getting Customer Recom Drool Properties for customer ID :{}", request.getCustomerId());
        stopwatch.start();
        CustomerRecommendationDroolProperties customerRecommendationDroolProperties = customerFavouriteProductsDao.getCustomerRecomDroolProperties(request.getCustomerId(), Objects.equals(request.getCustType(), "NEW"));
        log.info("&&&&&& ::: Step 1 : Got Customer Recom Drool Properties for customer ID :{} and its details are :::{} in :{}", request.getCustomerId(), new Gson().toJson(customerRecommendationDroolProperties), stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
        stopwatch.start();
        log.info("Starting cart-based recommendations for customer: {} at unit: {}",
                request.getCustomerId(), request.getUnitId());

        try {
            // Step 1: Build cart rules decision properties from request
            CartRulesDroolDecisionProperties cartRulesDecision = buildCartRulesDecision(request, customerRecommendationDroolProperties);
            log.info("Built cart rules decision: {}", new Gson().toJson(cartRulesDecision));
            log.info("&&&&&& ::: Step 1 : Got Customer Recom Drool Properties for customer ID :{} and its details are :::{} in :{}", request.getCustomerId(), new Gson().toJson(customerRecommendationDroolProperties), stopwatch.stop().elapsed(TimeUnit.MILLISECONDS));
            stopwatch.start();

            // Step 2: Execute cart rules and get cart rules result with list of matched rules
            String cartDroolVersion = Objects.nonNull(unitDroolVersionMapping) && unitDroolVersionMapping.containsKey(DroolFileType.CART_RULES_DROOL_DECISION.name()) ?
                    unitDroolVersionMapping.get(DroolFileType.CART_RULES_DROOL_DECISION.name()).getVersion() : null;
            CartRulesDroolDecisionProperties cartRulesDroolDecisionProperties = cartRulesOrchestrationService
                    .executeCartRulesDecision(cartRulesDecision, cartDroolVersion);
            log.info("Executed cart rules decision: {} matched rules", cartRulesDroolDecisionProperties.getInputRulesToBeRun());

            // Step 2.1: Execute cart rules and get cart rules result with list of matched rules
            String inputRulesDroolversion = Objects.nonNull(unitDroolVersionMapping) && unitDroolVersionMapping.containsKey(DroolFileType.OUTPUT_RULES_DROOL_DECISION.name()) ?
                    unitDroolVersionMapping.get(DroolFileType.INPUT_RULES_DROOL_DECISION.name()).getVersion() : null;
            List<InputRulesDroolDecisionProperties> inputRules = cartRulesOrchestrationService
                    .executeInputRulesFromCartResult(cartRulesDroolDecisionProperties, inputRulesDroolversion);
            log.info("Executed {} output rules from cart rules", inputRules.size());


            // Step 3: Execute output rules based on cart rules result

            String version = Objects.nonNull(unitDroolVersionMapping) && unitDroolVersionMapping.containsKey(DroolFileType.OUTPUT_RULES_DROOL_DECISION.name()) ?
                    unitDroolVersionMapping.get(DroolFileType.OUTPUT_RULES_DROOL_DECISION.name()).getVersion() : null;
            List<OutputRulesDroolDecisionProperties> outputRules = cartRulesOrchestrationService
                    .executeOutputRulesFromCartResult(new InputRulesDroolDecisionProperties(), version);
            log.info("Executed {} output rules from cart rules", outputRules.size());


            // Step 4: Generate recommendations from output rules
            List<RecommendedProduct> recommendedProducts = generateRecommendationsFromRules(outputRules, request);
            log.info("Generated {} recommended products", recommendedProducts.size());

            // Step 5: Organize products by category
            Map<Integer, List<RecommendedProduct>> categoryProductMap = organizeByCategoryMap(recommendedProducts);
            List<Integer> categorySequence = generateCategorySequence(categoryProductMap);

            // Step 6: Extract applied discounts
            List<CartRulesRecommendationResponse.AppliedDiscount> appliedDiscounts = extractAppliedDiscounts(outputRules);

            // Step 7: Build execution metadata
            CartRulesRecommendationResponse.ExecutionMetadata executionMetadata = buildExecutionMetadata(
                    stopwatch, version, outputRules, cartRulesDecision, cartRulesResult);

            return CartRulesRecommendationResponse.builder()
                    .cartRulesDecision(cartRulesDecision)
                    .cartRulesResult(cartRulesResult)
                    .outputRules(outputRules)
                    .recommendedProducts(recommendedProducts)
                    .categoryProductMap(categoryProductMap)
                    .categorySequence(categorySequence)
                    .appliedDiscounts(appliedDiscounts)
                    .executionMetadata(executionMetadata)
                    .build();

        } catch (Exception e) {
            log.error("Error processing cart-based recommendations for customer: {}", request.getCustomerId(), e);
            return buildErrorResponse(request, stopwatch);
        }
    }

    /**
     * Build cart rules decision properties from request
     */
    private CartRulesDroolDecisionProperties buildCartRulesDecision(CartRulesRecommendationRequest request, CustomerRecommendationDroolProperties customerRecommendationDroolProperties) throws DataUpdationException {

        OrderDetail orderDetail = orderDetailDao.findTopByCustomerId(request.getCustomerId(),  AppConstants.SETTLED);
        // Get customer information if not provided
        BigDecimal minPreviousATV = orderDetail.getTaxableAmount();
        BigDecimal maxPreviousATV = orderDetail.getTaxableAmount();
        String customerFrequency = customerRecommendationDroolProperties.getCustVisitType();
        String lastOrderCategory = customerRecommendationDroolProperties.getDineInFoodClassPreference();
        // Determine time of day if not provided
        String timeOfDay = Objects.nonNull(request.getDayPart()) ?
                request.getDayPart() :
                RecommendationDayPart.getCurrentDayPart(AppUtils.getCurrentTimestamp()).name();

        return CartRulesDroolDecisionProperties.builder()
                .lastOrderCategory(Objects.nonNull(lastOrderCategory) ? lastOrderCategory : "Default")
                .minPreviousCartATV(Objects.nonNull(minPreviousATV) ? minPreviousATV : BigDecimal.ZERO)
                .maxPreviousCartATV(Objects.nonNull(maxPreviousATV) ? maxPreviousATV : BigDecimal.ZERO)
                .customerFrequency(Objects.nonNull(customerFrequency) ? customerFrequency : "Default")
                .timeOfDay(timeOfDay)
                .customerNumberAvailable(Objects.nonNull(request.getCustomerNumberAvailable()) ?
                        request.getCustomerNumberAvailable() : true)
                .build();
    }

    /**
     * Generate recommendations from executed output rules
     */
    private List<RecommendedProduct> generateRecommendationsFromRules(
            List<OutputRulesDroolDecisionProperties> outputRules,
            CartRulesRecommendationRequest request) {

        List<RecommendedProduct> allRecommendations = new ArrayList<>();
        Map<Integer, ProductVO> availableProducts = productCache.getProductByUnit(request.getUnitId());

        for (OutputRulesDroolDecisionProperties rule : outputRules) {
            List<RecommendedProduct> ruleRecommendations = generateRecommendationsFromRule(rule, availableProducts);
            allRecommendations.addAll(ruleRecommendations);
        }

        // Remove duplicates and limit results
        return allRecommendations.stream()
                .collect(Collectors.toMap(
                        RecommendedProduct::getProductId,
                        product -> product,
                        (existing, replacement) -> existing))
                .values()
                .stream()
                .limit(20) // Limit to 20 recommendations
                .collect(Collectors.toList());
    }

    /**
     * Generate recommendations from a single output rule
     */
    private List<RecommendedProduct> generateRecommendationsFromRule(
            OutputRulesDroolDecisionProperties rule,
            Map<Integer, ProductVO> availableProducts) {

        List<RecommendedProduct> recommendations = new ArrayList<>();
        List<Integer> productIds = rule.getProductIDsList();

        for (Integer productId : productIds) {
            if (availableProducts.containsKey(productId)) {
                ProductVO product = availableProducts.get(productId);

                RecommendedProduct recommendation = RecommendedProduct.builder()
                        .productId(productId)
                        .categoryId(product.getType())
                        .recomReason(rule.getRecommendationType())
                        .build();

                recommendations.add(recommendation);

                // Limit per rule
                if (recommendations.size() >= rule.getProductCount()) {
                    break;
                }
            }
        }

        return recommendations;
    }

    /**
     * Organize products by category
     */
    private Map<Integer, List<RecommendedProduct>> organizeByCategoryMap(List<RecommendedProduct> products) {
        return products.stream()
                .collect(Collectors.groupingBy(RecommendedProduct::getCategoryId));
    }

    /**
     * Generate category sequence for display
     */
    private List<Integer> generateCategorySequence(Map<Integer, List<RecommendedProduct>> categoryMap) {
        return new ArrayList<>(categoryMap.keySet());
    }

    /**
     * Extract applied discounts from output rules
     */
    private List<CartRulesRecommendationResponse.AppliedDiscount> extractAppliedDiscounts(
            List<OutputRulesDroolDecisionProperties> outputRules) {

        return outputRules.stream()
                .filter(rule -> Boolean.TRUE.equals(rule.getDiscountApplicable()))
                .map(rule -> CartRulesRecommendationResponse.AppliedDiscount.builder()
                        .ruleNumber(rule.getRulesNumber())
                        .discountType(rule.getType())
                        .discountRules(rule.getDiscountRules())
                        .discountAmount(Objects.nonNull(rule.getDiscountAmount()) ?
                                rule.getDiscountAmount().toString() : "0")
                        .applicableProductIds(rule.getProductIDsList())
                        .description(rule.getRecommendationType() + " discount")
                        .build())
                .collect(Collectors.toList());
    }

    /**
     * Build execution metadata
     */
    private CartRulesRecommendationResponse.ExecutionMetadata buildExecutionMetadata(
            Stopwatch stopwatch, String version, List<OutputRulesDroolDecisionProperties> outputRules,
            CartRulesDroolDecisionProperties cartRulesDecision, CartRulesDecisionInputResult cartRulesResult) {

        return CartRulesRecommendationResponse.ExecutionMetadata.builder()
                .executionTimeMs(stopwatch.elapsed(TimeUnit.MILLISECONDS))
                .droolVersion(version)
                .totalRulesEvaluated(cartRulesResult.getTotalRulesEvaluated())
                .rulesExecuted(outputRules.size())
                .executionTimestamp(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                .executedRuleNumbers(outputRules.stream()
                        .map(rule -> rule.getRulesNumber().toString())
                        .collect(Collectors.toList()))
                .customerSegment(cartRulesDecision.getCustomerFrequency())
                .timeOfDay(cartRulesDecision.getTimeOfDay())
                .build();
    }

    /**
     * Build error response
     */
    private CartRulesRecommendationResponse buildErrorResponse(
            CartRulesRecommendationRequest request, Stopwatch stopwatch) {

        return CartRulesRecommendationResponse.builder()
                .cartRulesDecision(null)
                .outputRules(List.of())
                .recommendedProducts(List.of())
                .categoryProductMap(Map.of())
                .categorySequence(List.of())
                .appliedDiscounts(List.of())
                .executionMetadata(CartRulesRecommendationResponse.ExecutionMetadata.builder()
                        .executionTimeMs(stopwatch.elapsed(TimeUnit.MILLISECONDS))
                        .droolVersion("error")
                        .totalRulesEvaluated(0)
                        .rulesExecuted(0)
                        .executionTimestamp(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                        .build())
                .build();
    }

    /**
     * Execute cart rules with drool container and build complete decision result
     * This method uses the drool container to get all possible matches and then builds the complete result
     */
    private CartRulesDecisionInputResult executeCartRulesWithDroolAndBuildDecision(
            CartRulesDroolDecisionProperties cartRulesDecision,
            CartRulesRecommendationRequest request,
            String version) {

        log.info("Executing cart rules with drool container to get all possible matches");

        // Step 1: Use drool container to get all possible cart rule matches
        List<CartRulesDecisionInputResult.CartRule> droolMatchedRules = cartRulesOrchestrationService
                .cartRulesDroolInitialiser.initialiseDroolContainerAndReturnMatches(cartRulesDecision, version);

        log.info("Drool container returned {} matched cart rules", droolMatchedRules.size());

        // Step 2: Extract frontend parameters for output rule generation
        String currentCartState = cartRulesDecision.getCurrentCartState();
        BigDecimal minCurrentCartATV = cartRulesDecision.getMinCurrentCartATV();
        BigDecimal maxCurrentCartATV = cartRulesDecision.getMaxCurrentCartATV();

        // Step 3: Generate output rules from each matched cart rule
        List<OutputRulesDroolDecisionProperties> allOutputRules = new ArrayList<>();

        for (CartRulesDecisionInputResult.CartRule cartRule : droolMatchedRules) {
            // Feed each cart rule into output rule decision properties one by one
            List<OutputRulesDroolDecisionProperties> outputRulesForThisCartRule =
                    generateOutputRulesFromCartRuleString(cartRule.getOutputRulesToBeRun(),
                            cartRule, currentCartState, minCurrentCartATV, maxCurrentCartATV, version);

            allOutputRules.addAll(outputRulesForThisCartRule);

            log.info("Cart Rule {}: {} generated {} output rules",
                    cartRule.getRuleId(), cartRule.getRuleName(), outputRulesForThisCartRule.size());
        }

        // Step 4: Build final result with both cart rules and output rules
        CartRulesDecisionInputResult result = CartRulesDecisionInputResult.builder()
                .inputConditions(cartRulesDecision)
                .matchedCartRules(droolMatchedRules)
                .totalRulesEvaluated(16) // Total rules evaluated by drool
                .totalRulesMatched(droolMatchedRules.size())
                .executionTimestamp(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                .outputRules(allOutputRules)
                .build();

        log.info("Built complete cart rule decision from drool: {} cart rules, {} output rules",
                result.getTotalRulesMatched(), allOutputRules.size());

        return result;
    }

    /**
     * Generate output rules from cart rule output string
     */
    private List<OutputRulesDroolDecisionProperties> generateOutputRulesFromCartRuleString(
            String outputRulesString, CartRulesDecisionInputResult.CartRule cartRule,
            String currentCartState, BigDecimal minCurrentCartATV, BigDecimal maxCurrentCartATV, String version) {

        List<OutputRulesDroolDecisionProperties> outputRules = new ArrayList<>();

        if (outputRulesString == null || outputRulesString.trim().isEmpty()) {
            return outputRules;
        }

        // Parse output rule numbers from cart rule
        String[] outputRuleNumbers = outputRulesString.split(",");

        for (String ruleNumberStr : outputRuleNumbers) {
            try {
                Integer ruleNumber = Integer.parseInt(ruleNumberStr.trim());

                // Create output rule based on rule number and cart context
                OutputRulesDroolDecisionProperties outputRule = createOutputRuleFromNumberWithCartContext(
                        ruleNumber, cartRule, currentCartState, minCurrentCartATV, maxCurrentCartATV);

                if (outputRule != null) {
                    outputRules.add(outputRule);
                    log.debug("Generated output rule {} from cart rule {} ({})",
                            ruleNumber, cartRule.getRuleId(), cartRule.getRuleName());
                }
            } catch (NumberFormatException e) {
                log.warn("Invalid output rule number: {} in cart rule {}", ruleNumberStr, cartRule.getRuleId());
            }
        }

        return outputRules;
    }

    /**
     * Create output rule from rule number with cart rule context
     */
    private OutputRulesDroolDecisionProperties createOutputRuleFromNumberWithCartContext(
            Integer ruleNumber, CartRulesDecisionInputResult.CartRule cartRule,
            String currentCartState, BigDecimal minCurrentCartATV, BigDecimal maxCurrentCartATV) {

        // Enhanced output rule creation with cart rule context
        return OutputRulesDroolDecisionProperties.builder()
                .rulesNumber(ruleNumber)
                .type(getOutputRuleTypeWithContext(ruleNumber, cartRule))
                .recommendationType(getRecommendationTypeWithContext(ruleNumber, cartRule))
                .productIDs(getProductIDsWithContext(ruleNumber, cartRule))
                .productCount(getProductCountWithContext(ruleNumber, cartRule))
                .productType(getProductTypeWithContext(ruleNumber, cartRule))
                .vegNonVeg(getVegNonVegWithContext(ruleNumber, cartRule))
                .minPriceOfProduct(getMinPriceWithContext(ruleNumber, cartRule, minCurrentCartATV))
                .maxPriceOfProduct(getMaxPriceWithContext(ruleNumber, cartRule, maxCurrentCartATV))
                .discountApplicable(getDiscountApplicableWithContext(ruleNumber, cartRule))
                .discountRules(getDiscountRulesWithContext(ruleNumber, cartRule))
                .discountAmount(getDiscountAmountWithContext(ruleNumber, cartRule))
                .build();
    }

    // Enhanced helper methods that consider cart rule context
    private String getOutputRuleTypeWithContext(Integer ruleNumber, CartRulesDecisionInputResult.CartRule cartRule) {
        if ("PREMIUM_CUSTOMER".equals(cartRule.getRuleCategory()) || "LOYAL_CUSTOMER".equals(cartRule.getRuleCategory())) {
            return ruleNumber % 2 == 0 ? "DISCOUNT" : "PROMOTION";
        } else if ("NEW_CUSTOMER".equals(cartRule.getRuleCategory())) {
            return "RECOMMENDATION";
        } else {
            return getOutputRuleType(ruleNumber);
        }
    }

    private String getRecommendationTypeWithContext(Integer ruleNumber, CartRulesDecisionInputResult.CartRule cartRule) {
        if ("NEW_CUSTOMER".equals(cartRule.getRuleCategory())) {
            return "NEW_LAUNCH";
        } else if ("TIME_BASED".equals(cartRule.getRuleCategory())) {
            return "DAY_PART_SPECIAL";
        } else if ("PREMIUM_CUSTOMER".equals(cartRule.getRuleCategory())) {
            return "CUSTOMER_TOP_RATED";
        } else {
            return getRecommendationType(ruleNumber);
        }
    }

    private String getProductIDsWithContext(Integer ruleNumber, CartRulesDecisionInputResult.CartRule cartRule) {
        if ("CATEGORY_BASED".equals(cartRule.getRuleCategory())) {
            String lastOrderCategory = cartRule.getConditions().getLastOrderCategory();
            return getProductIDsForCategory(lastOrderCategory, ruleNumber);
        } else {
            return getProductIDs(ruleNumber);
        }
    }

    private Integer getProductCountWithContext(Integer ruleNumber, CartRulesDecisionInputResult.CartRule cartRule) {
        if ("PREMIUM_CUSTOMER".equals(cartRule.getRuleCategory()) || "LOYAL_CUSTOMER".equals(cartRule.getRuleCategory())) {
            return Math.min(5, getProductCount(ruleNumber) + 1);
        } else {
            return getProductCount(ruleNumber);
        }
    }

    private String getProductTypeWithContext(Integer ruleNumber, CartRulesDecisionInputResult.CartRule cartRule) {
        if (cartRule.getConditions() != null && cartRule.getConditions().getLastOrderCategory() != null) {
            String category = cartRule.getConditions().getLastOrderCategory();
            return switch (category.toUpperCase()) {
                case "HOT" -> "HOT";
                case "COLD" -> "COLD";
                case "FOOD" -> "FOOD";
                case "BAKERY" -> "BAKERY";
                case "BEVERAGE" -> ruleNumber % 2 == 0 ? "HOT" : "COLD";
                default -> getProductType(ruleNumber);
            };
        } else {
            return getProductType(ruleNumber);
        }
    }

    private String getVegNonVegWithContext(Integer ruleNumber, CartRulesDecisionInputResult.CartRule cartRule) {
        return getVegNonVeg(ruleNumber);
    }

    private BigDecimal getMinPriceWithContext(Integer ruleNumber, CartRulesDecisionInputResult.CartRule cartRule, BigDecimal minCurrentCartATV) {
        BigDecimal basePrice = getMinPrice(ruleNumber);
        if (minCurrentCartATV != null && minCurrentCartATV.compareTo(new BigDecimal("300")) > 0) {
            return basePrice.multiply(new BigDecimal("1.2"));
        }
        return basePrice;
    }

    private BigDecimal getMaxPriceWithContext(Integer ruleNumber, CartRulesDecisionInputResult.CartRule cartRule, BigDecimal maxCurrentCartATV) {
        BigDecimal basePrice = getMaxPrice(ruleNumber);
        if (maxCurrentCartATV != null && maxCurrentCartATV.compareTo(new BigDecimal("500")) > 0) {
            return basePrice.multiply(new BigDecimal("1.5"));
        }
        return basePrice;
    }

    private Boolean getDiscountApplicableWithContext(Integer ruleNumber, CartRulesDecisionInputResult.CartRule cartRule) {
        if ("PREMIUM_CUSTOMER".equals(cartRule.getRuleCategory()) || "LOYAL_CUSTOMER".equals(cartRule.getRuleCategory())) {
            return true;
        } else {
            return getDiscountApplicable(ruleNumber);
        }
    }

    private String getDiscountRulesWithContext(Integer ruleNumber, CartRulesDecisionInputResult.CartRule cartRule) {
        if (!getDiscountApplicableWithContext(ruleNumber, cartRule)) return null;

        if ("PREMIUM_CUSTOMER".equals(cartRule.getRuleCategory())) {
            return "PERCENTAGE";
        } else {
            return getDiscountRules(ruleNumber);
        }
    }

    private BigDecimal getDiscountAmountWithContext(Integer ruleNumber, CartRulesDecisionInputResult.CartRule cartRule) {
        if (!getDiscountApplicableWithContext(ruleNumber, cartRule)) return BigDecimal.ZERO;

        if ("PREMIUM_CUSTOMER".equals(cartRule.getRuleCategory())) {
            return new BigDecimal("20.00");
        } else if ("LOYAL_CUSTOMER".equals(cartRule.getRuleCategory())) {
            return new BigDecimal("15.00");
        } else {
            return getDiscountAmount(ruleNumber);
        }
    }

    private String getProductIDsForCategory(String category, Integer ruleNumber) {
        if (category == null) return getProductIDs(ruleNumber);

        return switch (category.toUpperCase()) {
            case "HOT" -> "10,11,12,50,51";
            case "COLD" -> "200,201,202,250,251";
            case "FOOD" -> "780,790,800,850,860";
            case "BAKERY" -> "500,501,502,550,551";
            case "MIXED" -> "10,200,780,500,1000";
            default -> getProductIDs(ruleNumber);
        };
    }
}
