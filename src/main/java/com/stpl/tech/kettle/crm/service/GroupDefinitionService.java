package com.stpl.tech.kettle.crm.service;

import com.stpl.tech.kettle.domain.kettle.model.request.CartRulesRecommendationRequest;

/**
 * Service interface for Group Definition sheet processing
 * Handles the logic to determine which input rules should be executed
 * based on customer and cart context
 */
public interface GroupDefinitionService {

    /**
     * Get input rules from Group Definition sheet logic
     * Returns underscore-separated string of rule numbers to execute
     * 
     * @param request Cart rules recommendation request
     * @return Underscore-separated string (e.g., "1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16")
     */
    String getInputRulesFromGroupDefinition(CartRulesRecommendationRequest request);

    /**
     * Validate if Group Definition result is in correct format
     * 
     * @param groupDefinitionResult Result to validate
     * @return true if format is valid
     */
    boolean isValidGroupDefinitionResult(String groupDefinitionResult);

    /**
     * Get default input rules for fallback scenarios
     * 
     * @return Default underscore-separated rule numbers
     */
    String getDefaultInputRules();
}
