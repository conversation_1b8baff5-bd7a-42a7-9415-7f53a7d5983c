package com.stpl.tech.kettle.crm.service.impl;

import com.stpl.tech.kettle.crm.service.DroolInitialiserService;
import com.stpl.tech.kettle.crm.service.InputRulesProcessingService;
import com.stpl.tech.kettle.domain.kettle.model.CartRulesDecisionResult;
import com.stpl.tech.kettle.domain.kettle.model.InputRulesDroolDecisionProperties;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementation of InputRulesProcessingService
 * Processes input rules from Group Definition sheet and returns rule definitions
 */
@Service
@Log4j2
public class InputRulesProcessingServiceImpl implements InputRulesProcessingService {

    @Autowired
    @Qualifier("inputRulesDroolInitialiser")
    private DroolInitialiserService<InputRulesDroolDecisionProperties> inputRulesDroolInitialiser;

    @Override
    public List<InputRulesDroolDecisionProperties> processInputRules(CartRulesDecisionResult cartRulesResult, String version) {
        if (cartRulesResult == null || cartRulesResult.getInputConditions() == null) {
            log.warn("CartRulesDecisionResult or inputConditions is null");
            return List.of();
        }

        String inputRulesString = cartRulesResult.getInputConditions().getInputRulesToBeRun();
        if (inputRulesString == null || inputRulesString.trim().isEmpty()) {
            log.warn("No input rules to process");
            return List.of();
        }

        // Parse the underscore-separated string and set in CartRulesDecisionResult
        List<Integer> inputRuleNumbers = parseInputRuleNumbers(inputRulesString);
        cartRulesResult.setInputRuleNumbers(inputRuleNumbers);

        log.info("Processing {} input rules from string: {}", inputRuleNumbers.size(), inputRulesString);

        // Process each input rule number
        return processInputRuleNumbers(inputRuleNumbers, version);
    }

    @Override
    public List<InputRulesDroolDecisionProperties> processInputRulesFromString(String inputRulesString, String version) {
        List<Integer> inputRuleNumbers = parseInputRuleNumbers(inputRulesString);
        log.info("Processing {} input rules from string: {}", inputRuleNumbers.size(), inputRulesString);
        return processInputRuleNumbers(inputRuleNumbers, version);
    }

    @Override
    public InputRulesDroolDecisionProperties getInputRuleDefinition(Integer ruleNumber, String version) {
        if (ruleNumber == null) {
            log.warn("Rule number is null");
            return null;
        }

        try {
            // Create InputRulesDroolDecisionProperties with the rule number
            InputRulesDroolDecisionProperties inputRule = InputRulesDroolDecisionProperties.builder()
                    .ruleNum(ruleNumber)
                    .build();

            // Execute the drool to get the rule definition
            inputRulesDroolInitialiser.initialiseDroolContainer(inputRule, version);

            log.info("Successfully retrieved definition for input rule number: {}", ruleNumber);
            return inputRule;

        } catch (Exception e) {
            log.error("Error processing input rule number {}: {}", ruleNumber, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<Integer> parseInputRuleNumbers(String inputRulesString) {
        if (inputRulesString == null || inputRulesString.trim().isEmpty()) {
            return List.of();
        }

        return List.of(inputRulesString.split("_"))
                .stream()
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(s -> {
                    try {
                        return Integer.parseInt(s);
                    } catch (NumberFormatException e) {
                        log.warn("Invalid rule number format: {}", s);
                        return null;
                    }
                })
                .filter(ruleNumber -> ruleNumber != null)
                .collect(Collectors.toList());
    }

    /**
     * Process a list of input rule numbers and return their definitions
     */
    private List<InputRulesDroolDecisionProperties> processInputRuleNumbers(List<Integer> inputRuleNumbers, String version) {
        List<InputRulesDroolDecisionProperties> inputRuleDefinitions = new ArrayList<>();

        for (Integer ruleNumber : inputRuleNumbers) {
            InputRulesDroolDecisionProperties ruleDefinition = getInputRuleDefinition(ruleNumber, version);
            if (ruleDefinition != null) {
                inputRuleDefinitions.add(ruleDefinition);
            }
        }

        log.info("Successfully processed {} out of {} input rules", 
                inputRuleDefinitions.size(), inputRuleNumbers.size());

        return inputRuleDefinitions;
    }
}
