package com.stpl.tech.kettle.crm.util;

import com.stpl.tech.kettle.domain.kettle.model.CartRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.CartRulesDecisionResult;
import com.stpl.tech.kettle.domain.kettle.model.InputRulesDroolDecisionProperties;
import lombok.extern.log4j.Log4j2;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Utility class for Input Rules Workflow operations
 * Provides helper methods for format conversion and validation
 */
@Log4j2
public class InputRulesWorkflowUtils {

    /**
     * Validates underscore-separated input rules string format
     * 
     * @param inputRulesString String to validate
     * @return true if format is valid
     */
    public static boolean isValidUnderscoreSeparatedFormat(String inputRulesString) {
        if (inputRulesString == null || inputRulesString.trim().isEmpty()) {
            return false;
        }

        // Check if string contains only numbers and underscores
        return inputRulesString.matches("^\\d+(_\\d+)*$");
    }

    /**
     * Converts underscore-separated string to List<Integer>
     * 
     * @param underscoreSeparatedString Input string like "1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16"
     * @return List<Integer> [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]
     */
    public static List<Integer> parseUnderscoreSeparatedString(String underscoreSeparatedString) {
        if (!isValidUnderscoreSeparatedFormat(underscoreSeparatedString)) {
            log.warn("Invalid underscore-separated format: {}", underscoreSeparatedString);
            return List.of();
        }

        return List.of(underscoreSeparatedString.split("_"))
                .stream()
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(s -> {
                    try {
                        return Integer.parseInt(s);
                    } catch (NumberFormatException e) {
                        log.warn("Invalid number format in string: {}", s);
                        return null;
                    }
                })
                .filter(number -> number != null)
                .collect(Collectors.toList());
    }

    /**
     * Converts List<Integer> to underscore-separated string
     * 
     * @param ruleNumbers List of rule numbers
     * @return Underscore-separated string
     */
    public static String convertToUnderscoreSeparatedString(List<Integer> ruleNumbers) {
        if (ruleNumbers == null || ruleNumbers.isEmpty()) {
            return "";
        }

        return ruleNumbers.stream()
                .filter(number -> number != null && number > 0)
                .map(String::valueOf)
                .collect(Collectors.joining("_"));
    }

    /**
     * Validates CartRulesDroolDecisionProperties for input rules processing
     * 
     * @param properties Properties to validate
     * @return true if valid for processing
     */
    public static boolean isValidForInputRulesProcessing(CartRulesDroolDecisionProperties properties) {
        if (properties == null) {
            log.warn("CartRulesDroolDecisionProperties is null");
            return false;
        }

        if (properties.getInputRulesToBeRun() == null || properties.getInputRulesToBeRun().trim().isEmpty()) {
            log.warn("inputRulesToBeRun is null or empty");
            return false;
        }

        if (!isValidUnderscoreSeparatedFormat(properties.getInputRulesToBeRun())) {
            log.warn("inputRulesToBeRun has invalid format: {}", properties.getInputRulesToBeRun());
            return false;
        }

        return true;
    }

    /**
     * Validates CartRulesDecisionResult for input rules processing
     * 
     * @param result Result to validate
     * @return true if valid for processing
     */
    public static boolean isValidForInputRulesProcessing(CartRulesDecisionResult result) {
        if (result == null) {
            log.warn("CartRulesDecisionResult is null");
            return false;
        }

        if (result.getInputRuleNumbers() == null || result.getInputRuleNumbers().isEmpty()) {
            log.warn("inputRuleNumbers is null or empty");
            return false;
        }

        // Check if all rule numbers are positive integers
        boolean allValid = result.getInputRuleNumbers().stream()
                .allMatch(number -> number != null && number > 0);

        if (!allValid) {
            log.warn("Some input rule numbers are invalid: {}", result.getInputRuleNumbers());
            return false;
        }

        return true;
    }

    /**
     * Creates a summary of input rules processing results
     * 
     * @param originalString Original underscore-separated string
     * @param parsedNumbers Parsed rule numbers
     * @param ruleDefinitions Retrieved rule definitions
     * @return Summary string
     */
    public static String createProcessingSummary(String originalString, 
                                                List<Integer> parsedNumbers, 
                                                List<InputRulesDroolDecisionProperties> ruleDefinitions) {
        StringBuilder summary = new StringBuilder();
        summary.append("Input Rules Processing Summary:\n");
        summary.append("Original String: ").append(originalString).append("\n");
        summary.append("Parsed Numbers: ").append(parsedNumbers).append("\n");
        summary.append("Total Rules Requested: ").append(parsedNumbers != null ? parsedNumbers.size() : 0).append("\n");
        summary.append("Total Rules Retrieved: ").append(ruleDefinitions != null ? ruleDefinitions.size() : 0).append("\n");
        
        if (ruleDefinitions != null && !ruleDefinitions.isEmpty()) {
            summary.append("Retrieved Rule Numbers: ");
            List<Integer> retrievedNumbers = ruleDefinitions.stream()
                    .map(InputRulesDroolDecisionProperties::getRuleNum)
                    .filter(num -> num != null)
                    .collect(Collectors.toList());
            summary.append(retrievedNumbers).append("\n");
        }
        
        return summary.toString();
    }

    /**
     * Extracts rule numbers that were successfully processed
     * 
     * @param ruleDefinitions List of rule definitions
     * @return List of successfully processed rule numbers
     */
    public static List<Integer> getSuccessfullyProcessedRuleNumbers(List<InputRulesDroolDecisionProperties> ruleDefinitions) {
        if (ruleDefinitions == null || ruleDefinitions.isEmpty()) {
            return List.of();
        }

        return ruleDefinitions.stream()
                .map(InputRulesDroolDecisionProperties::getRuleNum)
                .filter(ruleNum -> ruleNum != null)
                .collect(Collectors.toList());
    }

    /**
     * Finds rule numbers that failed to process
     * 
     * @param requestedNumbers Originally requested rule numbers
     * @param processedNumbers Successfully processed rule numbers
     * @return List of failed rule numbers
     */
    public static List<Integer> getFailedRuleNumbers(List<Integer> requestedNumbers, List<Integer> processedNumbers) {
        if (requestedNumbers == null || requestedNumbers.isEmpty()) {
            return List.of();
        }

        if (processedNumbers == null || processedNumbers.isEmpty()) {
            return requestedNumbers;
        }

        return requestedNumbers.stream()
                .filter(number -> !processedNumbers.contains(number))
                .collect(Collectors.toList());
    }

    /**
     * Calculates processing success rate
     * 
     * @param requestedCount Total requested rules
     * @param processedCount Successfully processed rules
     * @return Success rate as percentage (0.0 to 100.0)
     */
    public static double calculateSuccessRate(int requestedCount, int processedCount) {
        if (requestedCount == 0) {
            return 0.0;
        }
        return (double) processedCount / requestedCount * 100.0;
    }

    /**
     * Validates that a rule definition is complete
     * 
     * @param ruleDefinition Rule definition to validate
     * @return true if rule definition has all required fields
     */
    public static boolean isCompleteRuleDefinition(InputRulesDroolDecisionProperties ruleDefinition) {
        if (ruleDefinition == null) {
            return false;
        }

        // Check if essential fields are present
        return ruleDefinition.getRuleNum() != null &&
               ruleDefinition.getCustomerFrequency() != null &&
               ruleDefinition.getTimeOfDay() != null &&
               ruleDefinition.getCurrentCartState() != null;
    }

    /**
     * Filters out incomplete rule definitions
     * 
     * @param ruleDefinitions List of rule definitions
     * @return List containing only complete rule definitions
     */
    public static List<InputRulesDroolDecisionProperties> filterCompleteRuleDefinitions(
            List<InputRulesDroolDecisionProperties> ruleDefinitions) {
        
        if (ruleDefinitions == null || ruleDefinitions.isEmpty()) {
            return List.of();
        }

        return ruleDefinitions.stream()
                .filter(InputRulesWorkflowUtils::isCompleteRuleDefinition)
                .collect(Collectors.toList());
    }
}
