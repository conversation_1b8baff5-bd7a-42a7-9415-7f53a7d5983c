package com.stpl.tech.kettle.crm.service;

import com.stpl.tech.kettle.domain.kettle.model.request.CartRulesRecommendationRequest;

import java.util.Map;

/**
 * Service interface for reading Group Definition sheet from Excel file
 * Processes the Recommendation Rule Engine.xlsx file to extract input rules
 */
public interface GroupDefinitionExcelService {

    /**
     * Read Group Definition sheet from Excel file and return input rules
     * Reads from: D:\kettleCrm\crm\src\main\resources\Recommendation Rule Engine.xlsx
     * 
     * @param request Cart rules recommendation request for context
     * @return Underscore-separated string of input rule numbers (e.g., "1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16")
     */
    String getInputRulesFromGroupDefinitionSheet(CartRulesRecommendationRequest request);

    /**
     * Read and validate Group Definition sheet structure
     * 
     * @return true if sheet structure is valid
     */
    boolean validateGroupDefinitionSheetStructure();

    /**
     * Get all available group definitions from the sheet
     * 
     * @return Map of group conditions to input rule strings
     */
    Map<String, String> getAllGroupDefinitions();

    /**
     * Reload Excel file data (for cache refresh)
     */
    void reloadExcelData();

    /**
     * Get default input rules for fallback scenarios
     * 
     * @return Default underscore-separated rule numbers
     */
    String getDefaultInputRules();
}
