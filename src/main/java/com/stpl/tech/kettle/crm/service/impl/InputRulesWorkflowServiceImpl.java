package com.stpl.tech.kettle.crm.service.impl;

import com.stpl.tech.kettle.crm.service.InputRulesProcessingService;
import com.stpl.tech.kettle.crm.service.InputRulesWorkflowService;
import com.stpl.tech.kettle.domain.kettle.model.CartRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.CartRulesDecisionResult;
import com.stpl.tech.kettle.domain.kettle.model.InputRulesDroolDecisionProperties;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementation of complete input rules processing workflow
 * Orchestrates the entire sequence from Group Definition sheet to rule definitions
 */
@Service
@Log4j2
public class InputRulesWorkflowServiceImpl implements InputRulesWorkflowService {

    @Autowired
    private InputRulesProcessingService inputRulesProcessingService;

    @Override
    public InputRulesWorkflowResult executeCompleteWorkflow(String groupDefinitionResult, 
                                                          CartRulesDroolDecisionProperties cartRulesProperties, 
                                                          String version) {
        
        log.info("Starting complete input rules workflow for Group Definition result: {}", groupDefinitionResult);
        
        try {
            // Step 1-2: Extract and set input rules from Group Definition sheet
            CartRulesDroolDecisionProperties updatedCartRulesProperties = 
                setInputRulesFromGroupDefinition(groupDefinitionResult, cartRulesProperties);
            
            // Step 3-4: Parse underscore format and update CartRulesDecisionResult
            CartRulesDecisionResult cartRulesDecisionResult = 
                parseAndUpdateDecisionResult(updatedCartRulesProperties);
            
            // Step 5-6: Execute input rules and return definitions
            List<InputRulesDroolDecisionProperties> inputRuleDefinitions = 
                executeInputRulesAndGetDefinitions(cartRulesDecisionResult, version);
            
            // Parse rule numbers for result
            List<Integer> parsedRuleNumbers = parseUnderscoreSeparatedString(groupDefinitionResult);
            
            InputRulesWorkflowResult result = new InputRulesWorkflowResult(
                updatedCartRulesProperties,
                cartRulesDecisionResult,
                inputRuleDefinitions,
                parsedRuleNumbers,
                groupDefinitionResult
            );
            
            log.info("Successfully completed input rules workflow. Processed {} rules out of {} total", 
                    result.getTotalRulesProcessed(), parsedRuleNumbers.size());
            
            return result;
            
        } catch (Exception e) {
            log.error("Error in complete input rules workflow: {}", e.getMessage(), e);
            return new InputRulesWorkflowResult(cartRulesProperties, null, List.of(), List.of(), groupDefinitionResult);
        }
    }

    @Override
    public CartRulesDroolDecisionProperties setInputRulesFromGroupDefinition(String groupDefinitionResult, 
                                                                           CartRulesDroolDecisionProperties cartRulesProperties) {
        
        log.info("Step 1-2: Setting input rules from Group Definition result: {}", groupDefinitionResult);
        
        if (cartRulesProperties == null) {
            log.warn("CartRulesDroolDecisionProperties is null, creating new instance");
            cartRulesProperties = CartRulesDroolDecisionProperties.builder().build();
        }
        
        // Set the underscore-separated string in inputRulesToBeRun field
        cartRulesProperties.setInputRulesToBeRun(groupDefinitionResult);
        
        log.info("Successfully set inputRulesToBeRun: {}", groupDefinitionResult);
        return cartRulesProperties;
    }

    @Override
    public CartRulesDecisionResult parseAndUpdateDecisionResult(CartRulesDroolDecisionProperties cartRulesProperties) {
        
        log.info("Step 3-4: Parsing underscore format and updating CartRulesDecisionResult");
        
        if (cartRulesProperties == null || cartRulesProperties.getInputRulesToBeRun() == null) {
            log.warn("CartRulesProperties or inputRulesToBeRun is null");
            return CartRulesDecisionResult.builder()
                    .inputConditions(cartRulesProperties)
                    .inputRuleNumbers(List.of())
                    .executionTimestamp(getCurrentTimestamp())
                    .build();
        }
        
        // Parse the underscore-separated string to List<Integer>
        String inputRulesString = cartRulesProperties.getInputRulesToBeRun();
        List<Integer> parsedRuleNumbers = parseUnderscoreSeparatedString(inputRulesString);
        
        log.info("Parsed {} rule numbers from string '{}': {}", 
                parsedRuleNumbers.size(), inputRulesString, parsedRuleNumbers);
        
        // Create CartRulesDecisionResult and set the parsed list
        CartRulesDecisionResult result = CartRulesDecisionResult.builder()
                .inputConditions(cartRulesProperties)
                .inputRuleNumbers(parsedRuleNumbers)
                .totalRulesEvaluated(parsedRuleNumbers.size())
                .executionTimestamp(getCurrentTimestamp())
                .build();
        
        log.info("Successfully created CartRulesDecisionResult with {} input rule numbers", parsedRuleNumbers.size());
        return result;
    }

    @Override
    public List<InputRulesDroolDecisionProperties> executeInputRulesAndGetDefinitions(CartRulesDecisionResult cartRulesResult, 
                                                                                     String version) {
        
        log.info("Step 5-6: Executing input rules and getting definitions");
        
        if (cartRulesResult == null || cartRulesResult.getInputRuleNumbers() == null || 
            cartRulesResult.getInputRuleNumbers().isEmpty()) {
            log.warn("No input rule numbers to process");
            return List.of();
        }
        
        List<Integer> ruleNumbers = cartRulesResult.getInputRuleNumbers();
        log.info("Processing {} input rule numbers: {}", ruleNumbers.size(), ruleNumbers);
        
        // Use InputRulesProcessingService to get definitions for each rule number
        return inputRulesProcessingService.processInputRules(cartRulesResult, version);
    }

    @Override
    public List<Integer> parseUnderscoreSeparatedString(String underscoreSeparatedString) {
        return inputRulesProcessingService.parseInputRuleNumbers(underscoreSeparatedString);
    }

    @Override
    public String convertToUnderscoreSeparatedString(List<Integer> ruleNumbers) {
        if (ruleNumbers == null || ruleNumbers.isEmpty()) {
            return "";
        }
        
        return ruleNumbers.stream()
                .map(String::valueOf)
                .collect(Collectors.joining("_"));
    }
    
    /**
     * Get current timestamp for execution tracking
     */
    private String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}
