package com.stpl.tech.kettle.crm.service.impl;

import com.stpl.tech.kettle.domain.kettle.model.CartRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.CartRulesDecisionInputResult;
import com.stpl.tech.kettle.domain.kettle.model.OutputRulesDroolDecisionProperties;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Cart Rules Orchestration Service
 * Orchestrates the execution of cart rules decision and subsequent output rules
 * Provides a unified interface for cart-based rule processing
 */
@Service
@Log4j2
public class CartRulesOrchestrationService {

    @Autowired
    @Qualifier("cartRulesDroolInitialiser")
    private CartRulesDroolInitialiser cartRulesDroolInitialiser;

    @Autowired
    @Qualifier("inputRulesDroolInitialiser")
    private InputRulesDroolInitialiser inputRulesDroolInitialiser;

    @Autowired
    @Qualifier("outputRulesDroolInitialiser")
    private OutputRulesDroolInitialiser outputRulesDroolInitialiser;

    /**
     * Execute cart rules decision and return the cart rules result with list of matched rules
     *
     * @param cartRulesProperties Cart rules decision properties
     * @param version             Version of the drool files to use
     * @return Cart rules decision result with list of matched cart rules
     */
    public CartRulesDecisionInputResult executeCartRulesDecision(
            CartRulesDroolDecisionProperties cartRulesProperties,
            String version) {

        log.info("Starting cart rules decision execution for last order category: {} and customer frequency: {}",
                cartRulesProperties.getLastOrderCategory(), cartRulesProperties.getCustomerFrequency());

        // Step 1: Execute cart rules decision to get list of matched cart rules
        cartRulesDroolInitialiser.initialiseDroolContainer(cartRulesProperties, version);

        CartRulesDecisionInputResult result = CartRulesDecisionInputResult.builder()
                .inputConditions(cartRulesProperties)
                .matchedCartRules(matchedCartRules)
                .totalRulesEvaluated(10) // This would come from drool execution
                .totalRulesMatched(matchedCartRules.size())
                .executionTimestamp(java.time.LocalDateTime.now().toString())
                .build();

        log.info("Executed cart rules decision: {} matched rules out of {} evaluated",
                result.getTotalRulesMatched(), result.getTotalRulesEvaluated());
        return result;
    }

    /**
     * Execute output rules based on cart rules result
     *
     * @param cartRulesResult Cart rules decision result
     * @param version         Version of the drool files to use
     * @return List of executed output rules
     */
    public List<OutputRulesDroolDecisionProperties> executeOutputRulesFromCartResult(
            CartRulesDecisionInputResult cartRulesResult,
            String version) {

        if (!cartRulesResult.hasMatchedRules()) {
            log.info("No cart rules matched, no output rules to execute");
            return List.of();
        }

        // Get all output rule numbers from all matched cart rules
        List<Integer> allOutputRuleNumbers = cartRulesResult.getAllOutputRuleNumbers();
        log.info("Executing {} unique output rules from {} cart rules",
                allOutputRuleNumbers.size(), cartRulesResult.getTotalRulesMatched());

        // Execute each output rule
        List<OutputRulesDroolDecisionProperties> executedRules = allOutputRuleNumbers.stream()
                .map(ruleNumber -> executeOutputRule(ruleNumber, version))
                .filter(rule -> rule != null)
                .collect(Collectors.toList());

        log.info("Successfully executed {} output rules", executedRules.size());
        return executedRules;
    }

    /**
     * Execute output rules based on list of input rules
     *
     * @param inputRules List of input rules containing output rules to be executed
     * @param version    Version of the drool files to use
     * @return List of executed output rules combined from all input rules
     */
    public List<OutputRulesDroolDecisionProperties> executeOutputRulesFromCartResult(
            List<InputRulesDroolDecisionProperties> inputRules,
            String version) {

        if (inputRules == null || inputRules.isEmpty()) {
            log.info("No input rules provided, no output rules to execute");
            return List.of();
        }

        log.info("Executing output rules from {} input rules", inputRules.size());

        List<OutputRulesDroolDecisionProperties> allOutputRules = new ArrayList<>();

        for (InputRulesDroolDecisionProperties inputRule : inputRules) {
            if (inputRule.getOutputRulesToBeRun() != null && !inputRule.getOutputRulesToBeRun().trim().isEmpty()) {
                // Parse output rule numbers from input rule
                List<Integer> outputRuleNumbers = parseRuleNumbers(inputRule.getOutputRulesToBeRun());

                log.debug("Input rule {} has {} output rules to execute: {}",
                         inputRule.getRuleNum(), outputRuleNumbers.size(), outputRuleNumbers);

                // Execute each output rule for this input rule
                List<OutputRulesDroolDecisionProperties> outputRulesForThisInput = outputRuleNumbers.stream()
                        .map(ruleNumber -> executeOutputRule(ruleNumber, version))
                        .filter(rule -> rule != null)
                        .collect(Collectors.toList());

                allOutputRules.addAll(outputRulesForThisInput);

                log.debug("Input rule {} generated {} output rules",
                         inputRule.getRuleNum(), outputRulesForThisInput.size());
            }
        }

        log.info("Successfully executed {} total output rules from {} input rules",
                allOutputRules.size(), inputRules.size());
        return allOutputRules;
    }

    /**
     * Execute a specific output rule by rule number
     *
     * @param ruleNumber Rule number to execute
     * @param version    Version of the drool files to use
     * @return Executed output rule properties or null if execution failed
     */
    public OutputRulesDroolDecisionProperties executeOutputRule(Integer ruleNumber, String version) {
        try {
            // Create output rule properties with the rule number
            // In a real implementation, you would fetch the rule details from database or configuration
            OutputRulesDroolDecisionProperties outputRule = createOutputRuleFromRuleNumber(ruleNumber);

            // Execute the output rule
            outputRulesDroolInitialiser.initialiseDroolContainer(outputRule, version);

            log.info("Successfully executed output rule number: {}", ruleNumber);
            return outputRule;
        } catch (Exception e) {
            log.error("Failed to execute output rule number: {} with error: {}", ruleNumber, e.getMessage());
            return null;
        }
    }

    /**
     * Parse comma-separated rule numbers from string
     *
     * @param outputRulesToRun Comma-separated rule numbers
     * @return List of rule numbers
     */
    private List<Integer> parseRuleNumbers(String outputRulesToRun) {
        return Arrays.stream(outputRulesToRun.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(s -> {
                    try {
                        return Integer.parseInt(s);
                    } catch (NumberFormatException e) {
                        log.warn("Invalid rule number format: {}", s);
                        return null;
                    }
                })
                .filter(ruleNumber -> ruleNumber != null)
                .collect(Collectors.toList());
    }

    /**
     * Create output rule properties from rule number
     * This is a placeholder implementation - in reality, you would fetch rule details
     * from your recommendation rule engine configuration or database
     *
     * @param ruleNumber Rule number
     * @return Output rule properties
     */
    private OutputRulesDroolDecisionProperties createOutputRuleFromRuleNumber(Integer ruleNumber) {
        // This is a sample implementation - replace with actual rule configuration lookup
        return OutputRulesDroolDecisionProperties.builder()
                .rulesNumber(ruleNumber)
                .type("RECOMMENDATION") // Default type
                .recommendationType("CUSTOMER_TOP_RATED") // Default recommendation type
                .productCount(5) // Default product count
                .productType("HOT") // Default product type
                .vegNonVeg("BOTH") // Default dietary preference
                .discountApplicable(false) // Default no discount
                .build();
    }
}


