package com.stpl.tech.kettle.crm.service;

import com.stpl.tech.kettle.domain.kettle.model.CartRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.CartRulesDecisionResult;
import com.stpl.tech.kettle.domain.kettle.model.InputRulesDroolDecisionProperties;

import java.util.List;

/**
 * Service interface for complete input rules processing workflow
 * Handles the entire sequence from Group Definition sheet to rule definitions
 */
public interface InputRulesWorkflowService {

    /**
     * Complete workflow: Process input rules from Group Definition sheet format
     * 
     * Workflow Steps:
     * 1. Extract input rules from Group Definition sheet (underscore-separated format)
     * 2. Set in CartRulesDroolDecisionProperties.inputRulesToBeRun
     * 3. Parse and convert "1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16" to List<Integer>
     * 4. Update CartRulesDecisionResult.inputRuleNumbers
     * 5. Execute input rules processing via InputRulesDroolInitialiser
     * 6. Return complete rule definitions
     * 
     * @param groupDefinitionResult Underscore-separated string from Group Definition sheet
     * @param cartRulesProperties Cart rules properties to update
     * @param version Version of drool rules to use
     * @return InputRulesWorkflowResult containing all processed data
     */
    InputRulesWorkflowResult executeCompleteWorkflow(String groupDefinitionResult, 
                                                   CartRulesDroolDecisionProperties cartRulesProperties, 
                                                   String version);

    /**
     * Step 1-2: Extract and set input rules from Group Definition sheet
     * 
     * @param groupDefinitionResult Underscore-separated string (e.g., "1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16")
     * @param cartRulesProperties Cart rules properties to update
     * @return Updated CartRulesDroolDecisionProperties
     */
    CartRulesDroolDecisionProperties setInputRulesFromGroupDefinition(String groupDefinitionResult, 
                                                                     CartRulesDroolDecisionProperties cartRulesProperties);

    /**
     * Step 3-4: Parse underscore format and update CartRulesDecisionResult
     * 
     * @param cartRulesProperties Properties containing inputRulesToBeRun
     * @return CartRulesDecisionResult with parsed inputRuleNumbers
     */
    CartRulesDecisionResult parseAndUpdateDecisionResult(CartRulesDroolDecisionProperties cartRulesProperties);

    /**
     * Step 5-6: Execute input rules and return definitions
     * 
     * @param cartRulesResult Result containing inputRuleNumbers list
     * @param version Version of drool rules to use
     * @return List of InputRulesDroolDecisionProperties with complete rule definitions
     */
    List<InputRulesDroolDecisionProperties> executeInputRulesAndGetDefinitions(CartRulesDecisionResult cartRulesResult, 
                                                                              String version);

    /**
     * Utility: Parse underscore-separated string to List<Integer>
     * 
     * @param underscoreSeparatedString String like "1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16"
     * @return List<Integer> [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]
     */
    List<Integer> parseUnderscoreSeparatedString(String underscoreSeparatedString);

    /**
     * Utility: Convert List<Integer> to underscore-separated string
     * 
     * @param ruleNumbers List of rule numbers
     * @return Underscore-separated string
     */
    String convertToUnderscoreSeparatedString(List<Integer> ruleNumbers);

    /**
     * Result class containing all workflow data
     */
    class InputRulesWorkflowResult {
        private final CartRulesDroolDecisionProperties updatedCartRulesProperties;
        private final CartRulesDecisionResult cartRulesDecisionResult;
        private final List<InputRulesDroolDecisionProperties> inputRuleDefinitions;
        private final List<Integer> parsedRuleNumbers;
        private final String originalGroupDefinitionResult;

        public InputRulesWorkflowResult(CartRulesDroolDecisionProperties updatedCartRulesProperties,
                                      CartRulesDecisionResult cartRulesDecisionResult,
                                      List<InputRulesDroolDecisionProperties> inputRuleDefinitions,
                                      List<Integer> parsedRuleNumbers,
                                      String originalGroupDefinitionResult) {
            this.updatedCartRulesProperties = updatedCartRulesProperties;
            this.cartRulesDecisionResult = cartRulesDecisionResult;
            this.inputRuleDefinitions = inputRuleDefinitions;
            this.parsedRuleNumbers = parsedRuleNumbers;
            this.originalGroupDefinitionResult = originalGroupDefinitionResult;
        }

        // Getters
        public CartRulesDroolDecisionProperties getUpdatedCartRulesProperties() { return updatedCartRulesProperties; }
        public CartRulesDecisionResult getCartRulesDecisionResult() { return cartRulesDecisionResult; }
        public List<InputRulesDroolDecisionProperties> getInputRuleDefinitions() { return inputRuleDefinitions; }
        public List<Integer> getParsedRuleNumbers() { return parsedRuleNumbers; }
        public String getOriginalGroupDefinitionResult() { return originalGroupDefinitionResult; }

        public int getTotalRulesProcessed() { 
            return inputRuleDefinitions != null ? inputRuleDefinitions.size() : 0; 
        }

        public boolean isSuccessful() { 
            return inputRuleDefinitions != null && !inputRuleDefinitions.isEmpty(); 
        }
    }
}
