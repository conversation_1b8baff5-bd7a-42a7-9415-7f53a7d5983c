package com.stpl.tech.kettle.crm.service;

import com.stpl.tech.kettle.domain.kettle.model.CartRulesDecisionResult;
import com.stpl.tech.kettle.domain.kettle.model.InputRulesDroolDecisionProperties;

import java.util.List;

/**
 * Service interface for processing input rules from Group Definition sheet
 * Handles conversion from underscore-separated format to input rule definitions
 */
public interface InputRulesProcessingService {

    /**
     * Process input rules from CartRulesDecisionResult
     * Converts underscore-separated input rule numbers to list and executes each rule
     * 
     * @param cartRulesResult Result containing inputRulesToBeRun in format "1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16"
     * @param version Version of the drool rules to use
     * @return List of InputRulesDroolDecisionProperties with definitions for each rule number
     */
    List<InputRulesDroolDecisionProperties> processInputRules(CartRulesDecisionResult cartRulesResult, String version);

    /**
     * Process input rules from underscore-separated string
     * 
     * @param inputRulesString Underscore-separated rule numbers (e.g., "1_2_3_4_5")
     * @param version Version of the drool rules to use
     * @return List of InputRulesDroolDecisionProperties with definitions for each rule number
     */
    List<InputRulesDroolDecisionProperties> processInputRulesFromString(String inputRulesString, String version);

    /**
     * Get input rule definition for a specific rule number
     * 
     * @param ruleNumber Rule number to get definition for
     * @param version Version of the drool rules to use
     * @return InputRulesDroolDecisionProperties with definition for the rule number
     */
    InputRulesDroolDecisionProperties getInputRuleDefinition(Integer ruleNumber, String version);

    /**
     * Parse underscore-separated input rules string to list of integers
     * 
     * @param inputRulesString String in format "1_2_3_4_5_6_7_8_9_10_11_12_13_14_15_16"
     * @return List of rule numbers as integers
     */
    List<Integer> parseInputRuleNumbers(String inputRulesString);
}
