package com.stpl.tech.kettle.domain.kettle.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Cart Rules Decision Result
 * Contains the result of cart rules evaluation with a list of matched cart rules
 * Replaces the single rule approach with multiple rules support
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CartRulesDecisionInputResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Input conditions that were evaluated
     */
    private CartRulesDroolDecisionProperties inputConditions;

    /**
     * List of cart rules that matched the input conditions
     * Each rule contains its own set of output rules to execute
     */
    private List<CartRule> matchedCartRules;

    /**
     * Total number of rules evaluated
     */
    private Integer totalRulesEvaluated;

    /**
     * Total number of rules matched
     */
    private Integer totalRulesMatched;

    /**
     * Execution timestamp
     */
    private String executionTimestamp;

    /**
     * Individual Cart Rule that was matched
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CartRule implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * Unique identifier for this cart rule
         */
        private Integer ruleId;

        /**
         * Name/description of the cart rule
         */
        private String ruleName;

        /**
         * Priority of this rule (higher number = higher priority)
         */
        private Integer priority;

        /**
         * Category this rule applies to
         */
        private String ruleCategory;

        /**
         * Conditions that were matched for this rule
         */
        private MatchedConditions conditions;

        /**
         * Output rules to be executed for this cart rule
         */
        private String outputRulesToBeRun;

        /**
         * Whether this rule is currently active
         */
        private Boolean isActive;

        /**
         * Rule execution order
         */
        private Integer executionOrder;
    }

    /**
     * Matched Conditions for a specific cart rule
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MatchedConditions implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * Last order category that matched
         */
        private String lastOrderCategory;

        /**
         * Customer frequency that matched
         */
        private String customerFrequency;

        /**
         * Time of day that matched
         */
        private String timeOfDay;

        /**
         * Cart state that matched
         */
        private String cartState;

        /**
         * ATV range that matched
         */
        private String atvRange;

        /**
         * Customer number availability
         */
        private Boolean customerNumberAvailable;

        /**
         * Additional matching context
         */
        private String additionalContext;
    }

    /**
     * Get all output rule numbers from all matched cart rules
     */
    public List<Integer> getAllOutputRuleNumbers() {
        if (matchedCartRules == null || matchedCartRules.isEmpty()) {
            return List.of();
        }

        return matchedCartRules.stream()
                .filter(rule -> rule.getOutputRulesToBeRun() != null && !rule.getOutputRulesToBeRun().trim().isEmpty())
                .flatMap(rule -> parseOutputRuleNumbers(rule.getOutputRulesToBeRun()).stream())
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * Get cart rules sorted by priority (highest first)
     */
    public List<CartRule> getCartRulesByPriority() {
        if (matchedCartRules == null || matchedCartRules.isEmpty()) {
            return List.of();
        }

        return matchedCartRules.stream()
                .sorted((rule1, rule2) -> {
                    Integer priority1 = rule1.getPriority() != null ? rule1.getPriority() : 0;
                    Integer priority2 = rule2.getPriority() != null ? rule2.getPriority() : 0;
                    return priority2.compareTo(priority1); // Descending order
                })
                .collect(Collectors.toList());
    }

    /**
     * Get cart rules by category
     */
    public List<CartRule> getCartRulesByCategory(String category) {
        if (matchedCartRules == null || matchedCartRules.isEmpty()) {
            return List.of();
        }

        return matchedCartRules.stream()
                .filter(rule -> category.equals(rule.getRuleCategory()))
                .collect(Collectors.toList());
    }

    /**
     * Check if any cart rules were matched
     */
    public boolean hasMatchedRules() {
        return matchedCartRules != null && !matchedCartRules.isEmpty();
    }

    /**
     * Get summary of matched rules
     */
    public String getMatchedRulesSummary() {
        if (!hasMatchedRules()) {
            return "No cart rules matched";
        }

        return matchedCartRules.stream()
                .map(rule -> String.format("Rule[%d:%s]", rule.getRuleId(), rule.getRuleName()))
                .collect(Collectors.joining(", "));
    }

    /**
     * Parse output rule numbers from comma-separated string
     */
    private List<Integer> parseOutputRuleNumbers(String outputRules) {
        if (outputRules == null || outputRules.trim().isEmpty()) {
            return List.of();
        }

        return List.of(outputRules.split(","))
                .stream()
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(s -> {
                    try {
                        return Integer.parseInt(s);
                    } catch (NumberFormatException e) {
                        return null;
                    }
                })
                .filter(ruleNumber -> ruleNumber != null)
                .collect(Collectors.toList());
    }
}
