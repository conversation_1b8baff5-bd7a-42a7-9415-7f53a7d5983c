package com.stpl.tech.kettle.domain.kettle.model.response;

import com.stpl.tech.kettle.domain.kettle.model.CartRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.CartRulesDecisionInputResult;
import com.stpl.tech.kettle.domain.kettle.model.OutputRulesDroolDecisionProperties;
import com.stpl.tech.kettle.domain.kettle.model.RecommendedProduct;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Cart Rules Recommendation Response
 * Response model for cart-based recommendation API
 * Contains both cart rules decision and output rules with recommendations
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CartRulesRecommendationResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * Cart rules decision properties that were evaluated
     * Shows the input conditions that triggered the rules
     */
    private CartRulesDroolDecisionProperties cartRulesDecision;

    /**
     * Result of cart rules evaluation with list of matched cart rules
     * Contains all cart rules that matched the input conditions
     */
    private CartRulesDecisionInputResult cartRulesResult;

    /**
     * List of output rules that were determined and executed
     * Contains the rule definitions and their parameters
     */
    private List<OutputRulesDroolDecisionProperties> outputRules;

    /**
     * Final list of recommended products based on executed rules
     * Aggregated from all applicable output rules
     */
    private List<RecommendedProduct> recommendedProducts;

    /**
     * Category-wise product mapping for UI organization
     * Key: Category ID, Value: List of products in that category
     */
    @Builder.Default
    private Map<Integer, List<RecommendedProduct>> categoryProductMap = Map.of();

    /**
     * Sequence of categories for display order
     */
    @Builder.Default
    private List<Integer> categorySequence = List.of();

    /**
     * Summary of applied discounts and offers
     */
    private List<AppliedDiscount> appliedDiscounts;

    /**
     * Execution metadata for debugging and analytics
     */
    private ExecutionMetadata executionMetadata;

    /**
     * Applied Discount information
     */
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class AppliedDiscount implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private Integer ruleNumber;
        private String discountType;
        private String discountRules;
        private String discountAmount;
        private List<Integer> applicableProductIds;
        private String description;
    }

    /**
     * Execution Metadata for tracking and debugging
     */
    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ExecutionMetadata implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private Long executionTimeMs;
        private String droolVersion;
        private Integer totalRulesEvaluated;
        private Integer rulesExecuted;
        private String executionTimestamp;
        private List<String> executedRuleNumbers;
        private String customerSegment;
        private String timeOfDay;
    }
}
